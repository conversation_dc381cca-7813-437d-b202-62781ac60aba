<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "证件照制作"
  }
}
</route>

<script lang="ts" setup>
// import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets: any
let systemInfo: any

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 热门证件照类型
const hotPhotoTypes = [
  { id: 1, name: '国家公务员（小...', size: '25×35mm | 295×413px', hot: true },
  { id: 2, name: '一寸', size: '25×35mm | 295×413px', hot: true },
  { id: 3, name: '大一寸', size: '33×48mm | 390×567px', hot: false },
  { id: 4, name: '小一寸', size: '22×32mm | 260×378px', hot: false },
  { id: 5, name: '硕士研究生考...', size: '25×35mm | 295×413px', hot: true },
  { id: 6, name: '二寸', size: '35×49mm | 413×579px', hot: false },
  { id: 7, name: '简历照片', size: '25×35mm | 295×413px', hot: false },
  { id: 8, name: '国家司法考试', size: '25×35mm | 295×413px', hot: false },
  { id: 9, name: '教师资格证', size: '25×35mm | 295×413px', hot: false },
  { id: 10, name: '社保证（300dp...', size: '25×35mm | 295×413px', hot: false },
]

// 删除未使用的变量

// 跳转到制作页面
function goToMake(type: any) {
  uni.navigateTo({
    url: `/pages/make/index?type=${type.id}&name=${type.name}`,
  })
}

// 跳转到功能页面
function goToPage(path: string) {
  uni.navigateTo({
    url: path,
  })
}
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 简洁头部 -->
    <view class="bg-white px-6 py-8">
      <view class="mb-6 flex items-center justify-between">
        <view>
          <view class="mb-1 text-2xl text-gray-900 font-bold">
            证件照制作
          </view>
          <view class="text-sm text-gray-500">
            专业AI智能处理
          </view>
        </view>
        <view class="h-12 w-12 flex items-center justify-center rounded-2xl bg-blue-500">
          <view class="text-xl text-white">
            📷
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="px-6 pb-6">
      <!-- 快速制作入口 -->
      <view class="grid grid-cols-2 mb-8 gap-4">
        <view
          class="border border-gray-100 rounded-3xl bg-white p-6 shadow-sm transition-all duration-300 hover:shadow-md"
          @click="goToPage('/pages/camera/index')"
        >
          <view class="mb-4 h-12 w-12 flex items-center justify-center rounded-2xl bg-blue-100">
            <view class="text-2xl text-blue-600">
              📷
            </view>
          </view>
          <view class="mb-1 text-gray-900 font-semibold">
            拍照制作
          </view>
          <view class="text-sm text-gray-500">
            现拍现做
          </view>
        </view>

        <view
          class="border border-gray-100 rounded-3xl bg-white p-6 shadow-sm transition-all duration-300 hover:shadow-md"
          @click="goToPage('/pages/upload/index')"
        >
          <view class="mb-4 h-12 w-12 flex items-center justify-center rounded-2xl bg-purple-100">
            <view class="text-2xl text-purple-600">
              🖼️
            </view>
          </view>
          <view class="mb-1 text-gray-900 font-semibold">
            相册选择
          </view>
          <view class="text-sm text-gray-500">
            从相册选图
          </view>
        </view>
      </view>

      <!-- 热门证件照类型 -->
      <view class="mb-8 border border-gray-100 rounded-3xl bg-white p-6 shadow-sm">
        <view class="mb-6 flex items-center justify-between">
          <view class="flex items-center">
            <view class="text-lg text-gray-900 font-semibold">
              热门证件照
            </view>
            <view class="ml-2 rounded-full bg-red-100 px-2 py-1 text-xs text-red-600 font-medium">
              HOT
            </view>
          </view>
          <view class="flex items-center text-sm text-gray-400" @click="goToPage('/pages/more/index')">
            <view>查看全部</view>
            <view class="ml-1">
              ›
            </view>
          </view>
        </view>

        <!-- 热门证件照网格 -->
        <view class="space-y-3">
          <view
            v-for="(type, index) in hotPhotoTypes.slice(0, 6)"
            :key="type.id"
            class="flex items-center rounded-2xl bg-gray-50 p-4 transition-all duration-300 hover:bg-gray-100"
            @click="goToMake(type)"
          >
            <view class="mr-4 h-10 w-10 flex items-center justify-center rounded-xl bg-blue-500">
              <view class="text-sm text-white font-bold">
                {{ String(index + 1).padStart(2, '0') }}
              </view>
            </view>
            <view class="flex-1">
              <view class="mb-1 text-gray-900 font-medium">
                {{ type.name }}
              </view>
              <view class="text-sm text-gray-500">
                {{ type.size }}
              </view>
            </view>
            <view class="text-gray-400">
              <view class="text-lg">
                ›
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 功能特色 -->
      <view class="mb-6 border border-gray-100 rounded-3xl bg-white p-6 shadow-sm">
        <view class="mb-4 text-center">
          <view class="mb-2 text-lg text-gray-900 font-semibold">
            为什么选择我们
          </view>
          <view class="text-sm text-gray-500">
            专业AI技术，让证件照制作更简单
          </view>
        </view>

        <view class="grid grid-cols-3 gap-4">
          <view class="text-center">
            <view class="mx-auto mb-3 h-12 w-12 flex items-center justify-center rounded-2xl bg-green-100">
              <view class="text-xl text-green-600">
                🎯
              </view>
            </view>
            <view class="mb-1 text-sm text-gray-900 font-medium">
              精准识别
            </view>
            <view class="text-xs text-gray-500">
              AI智能识别
            </view>
          </view>

          <view class="text-center">
            <view class="mx-auto mb-3 h-12 w-12 flex items-center justify-center rounded-2xl bg-orange-100">
              <view class="text-xl text-orange-600">
                ⚡
              </view>
            </view>
            <view class="mb-1 text-sm text-gray-900 font-medium">
              快速生成
            </view>
            <view class="text-xs text-gray-500">
              秒速处理
            </view>
          </view>

          <view class="text-center">
            <view class="mx-auto mb-3 h-12 w-12 flex items-center justify-center rounded-2xl bg-pink-100">
              <view class="text-xl text-pink-600">
                🎨
              </view>
            </view>
            <view class="mb-1 text-sm text-gray-900 font-medium">
              智能美化
            </view>
            <view class="text-xs text-gray-500">
              专业品质
            </view>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="h-6" />
    </view>
  </view>
</template>

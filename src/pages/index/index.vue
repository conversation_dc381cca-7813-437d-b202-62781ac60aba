<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "证件照制作"
  }
}
</route>

<script lang="ts" setup>
import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 证件照相关状态
const selectedImage = ref('')
const isProcessing = ref(false)

// 常用证件照尺寸配置
const photoSizes = [
  { name: '一寸', width: 295, height: 413, desc: '2.5cm × 3.5cm' },
  { name: '二寸', width: 413, height: 579, desc: '3.5cm × 4.9cm' },
  { name: '小二寸', width: 413, height: 531, desc: '3.5cm × 4.5cm' },
  { name: '大一寸', width: 390, height: 567, desc: '3.3cm × 4.8cm' },
]

// 背景颜色选项
const backgroundColors = [
  { name: '白色', value: '#FFFFFF', class: 'bg-white border-gray-300' },
  { name: '红色', value: '#FF0000', class: 'bg-red-500' },
  { name: '蓝色', value: '#0000FF', class: 'bg-blue-500' },
  { name: '灰色', value: '#C0C0C0', class: 'bg-gray-400' },
]

const selectedSize = ref(photoSizes[0])
const selectedBgColor = ref(backgroundColors[0])

// 上传图片
const { loading: uploadLoading, run: uploadImage } = useUpload({
  fileType: 'image',
  maxSize: 10 * 1024 * 1024, // 10MB
  success: (data) => {
    selectedImage.value = data.url || data
    uni.showToast({
      title: '图片上传成功',
      icon: 'success',
    })
  },
  error: (err) => {
    console.error('上传失败:', err)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    })
  },
})

// 选择照片
function handleSelectPhoto() {
  uni.showActionSheet({
    itemList: ['拍照', '从相册选择'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 拍照
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          success: (res) => {
            selectedImage.value = res.tempFilePaths[0]
          },
        })
      }
      else {
        // 从相册选择
        uploadImage()
      }
    },
  })
}

// 制作证件照
function handleMakePhoto() {
  if (!selectedImage.value) {
    uni.showToast({
      title: '请先选择照片',
      icon: 'none',
    })
    return
  }

  isProcessing.value = true

  // 模拟处理过程
  setTimeout(() => {
    isProcessing.value = false
    uni.showToast({
      title: '证件照制作完成',
      icon: 'success',
    })
  }, 2000)
}

// 选择尺寸
function handleSelectSize(size: typeof photoSizes[0]) {
  selectedSize.value = size
}

// 选择背景色
function handleSelectBgColor(color: typeof backgroundColors[0]) {
  selectedBgColor.value = color
}
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 头部标题 -->
    <view class="bg-white px-4 py-4 shadow-sm">
      <view class="text-center text-xl text-gray-800 font-bold">
        证件照制作
      </view>
      <view class="mt-1 text-center text-sm text-gray-500">
        专业证件照，一键生成
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="px-4 py-6">
      <!-- 照片预览区域 -->
      <view class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <view class="mb-4 text-lg text-gray-800 font-semibold">
          照片预览
        </view>
        <view class="flex items-center justify-center">
          <view
            v-if="selectedImage"
            class="relative overflow-hidden border-2 border-gray-300 rounded-lg border-dashed"
            :style="{
              width: `${Math.min(selectedSize.width * 0.5, 200)}px`,
              height: `${Math.min(selectedSize.height * 0.5, 280)}px`,
              backgroundColor: selectedBgColor.value,
            }"
          >
            <image
              :src="selectedImage"
              mode="aspectFill"
              class="h-full w-full"
            />
          </view>
          <view
            v-else
            class="h-64 w-48 flex flex-col items-center justify-center border-2 border-gray-300 rounded-lg border-dashed bg-gray-50"
            @click="handleSelectPhoto"
          >
            <view class="mb-2 text-4xl text-gray-400">
              📷
            </view>
            <view class="text-sm text-gray-500">
              点击选择照片
            </view>
            <view class="mt-1 text-xs text-gray-400">
              支持拍照或相册选择
            </view>
          </view>
        </view>
      </view>

      <!-- 尺寸选择 -->
      <view class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <view class="mb-4 text-lg text-gray-800 font-semibold">
          选择尺寸
        </view>
        <view class="grid grid-cols-2 gap-3">
          <view
            v-for="size in photoSizes"
            :key="size.name"
            class="border rounded-lg p-3 text-center transition-all"
            :class="selectedSize.name === size.name ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
            @click="handleSelectSize(size)"
          >
            <view class="text-gray-800 font-medium">
              {{ size.name }}
            </view>
            <view class="mt-1 text-xs text-gray-500">
              {{ size.desc }}
            </view>
          </view>
        </view>
      </view>

      <!-- 背景颜色选择 -->
      <view class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <view class="mb-4 text-lg text-gray-800 font-semibold">
          背景颜色
        </view>
        <view class="flex justify-around">
          <view
            v-for="color in backgroundColors"
            :key="color.name"
            class="flex flex-col items-center"
            @click="handleSelectBgColor(color)"
          >
            <view
              class="mb-2 h-12 w-12 border-2 rounded-full transition-all"
              :class="[
                color.class,
                selectedBgColor.name === color.name ? 'border-blue-500 scale-110' : 'border-gray-300',
              ]"
            />
            <view class="text-xs text-gray-600">
              {{ color.name }}
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="space-y-3">
        <wd-button
          type="primary"
          size="large"
          block
          :loading="uploadLoading"
          @click="handleSelectPhoto"
        >
          {{ selectedImage ? '重新选择照片' : '选择照片' }}
        </wd-button>

        <wd-button
          type="success"
          size="large"
          block
          :loading="isProcessing"
          :disabled="!selectedImage"
          @click="handleMakePhoto"
        >
          {{ isProcessing ? '制作中...' : '制作证件照' }}
        </wd-button>
      </view>

      <!-- 功能说明 -->
      <view class="mt-8 rounded-lg bg-blue-50 p-4">
        <view class="mb-2 text-sm text-blue-800 font-medium">
          功能说明
        </view>
        <view class="text-xs text-blue-600 space-y-1">
          <view>• 支持多种常用证件照尺寸</view>
          <view>• 自动背景替换，支持白、红、蓝、灰色背景</view>
          <view>• 智能人像识别和裁剪</view>
          <view>• 高清输出，满足各种证件要求</view>
        </view>
      </view>
    </view>
  </view>
</template>

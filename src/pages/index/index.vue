<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "证件照制作"
  }
}
</route>

<script lang="ts" setup>
import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets: any
let systemInfo: any

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 证件照相关状态
const selectedImage = ref('')
const isProcessing = ref(false)

// 常用证件照尺寸配置
const photoSizes = [
  { name: '一寸', width: 295, height: 413, desc: '2.5cm × 3.5cm' },
  { name: '二寸', width: 413, height: 579, desc: '3.5cm × 4.9cm' },
  { name: '小二寸', width: 413, height: 531, desc: '3.5cm × 4.5cm' },
  { name: '大一寸', width: 390, height: 567, desc: '3.3cm × 4.8cm' },
]

// 背景颜色选项
const backgroundColors = [
  { name: '白色', value: '#FFFFFF', class: 'bg-white border-gray-300' },
  { name: '红色', value: '#FF0000', class: 'bg-red-500' },
  { name: '蓝色', value: '#0000FF', class: 'bg-blue-500' },
  { name: '灰色', value: '#C0C0C0', class: 'bg-gray-400' },
]

const selectedSize = ref(photoSizes[0])
const selectedBgColor = ref(backgroundColors[0])

// 上传图片
const { loading: uploadLoading, run: uploadImage } = useUpload({
  fileType: 'image',
  maxSize: 10 * 1024 * 1024, // 10MB
  success: (data) => {
    selectedImage.value = data.url || data
    uni.showToast({
      title: '图片上传成功',
      icon: 'success',
    })
  },
  error: (err) => {
    console.error('上传失败:', err)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    })
  },
})

// 选择照片
function handleSelectPhoto() {
  uni.showActionSheet({
    itemList: ['拍照', '从相册选择'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 拍照
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          success: (res) => {
            selectedImage.value = res.tempFilePaths[0]
          },
        })
      }
      else {
        // 从相册选择
        uploadImage()
      }
    },
  })
}

// 制作证件照
function handleMakePhoto() {
  if (!selectedImage.value) {
    uni.showToast({
      title: '请先选择照片',
      icon: 'none',
    })
    return
  }

  isProcessing.value = true

  // 模拟处理过程
  setTimeout(() => {
    isProcessing.value = false
    uni.showToast({
      title: '证件照制作完成',
      icon: 'success',
    })
  }, 2000)
}

// 选择尺寸
function handleSelectSize(size: typeof photoSizes[0]) {
  selectedSize.value = size
}

// 选择背景色
function handleSelectBgColor(color: typeof backgroundColors[0]) {
  selectedBgColor.value = color
}
</script>

<template>
  <view class="min-h-screen from-blue-50 to-indigo-100 bg-gradient-to-br" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 头部标题 -->
    <view class="relative from-blue-600 to-indigo-600 bg-gradient-to-r px-6 py-8">
      <view class="absolute inset-0 bg-black opacity-10" />
      <view class="relative z-10">
        <view class="mb-2 text-center text-2xl text-white font-bold">
          ✨ AI证件照制作
        </view>
        <view class="text-center text-sm text-blue-100">
          专业品质 · 一键生成 · 智能美化
        </view>
      </view>
      <!-- 装饰性波浪 -->
      <view class="absolute bottom-0 left-0 h-4 w-full from-blue-50 to-indigo-100 bg-gradient-to-br" style="clip-path: polygon(0 100%, 100% 0, 100% 100%)" />
    </view>

    <!-- 主要内容区域 -->
    <view class="px-4 py-6 -mt-2">
      <!-- 照片预览区域 -->
      <view class="mb-8 border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 flex items-center">
          <view class="mr-3 h-6 w-1 rounded-full from-blue-500 to-indigo-500 bg-gradient-to-b" />
          <view class="text-xl text-gray-800 font-bold">
            照片预览
          </view>
        </view>

        <view class="flex items-center justify-center">
          <view
            v-if="selectedImage"
            class="relative overflow-hidden border-4 border-white rounded-2xl shadow-xl"
            :style="{
              width: `${Math.min(selectedSize.width * 0.6, 240)}px`,
              height: `${Math.min(selectedSize.height * 0.6, 320)}px`,
              backgroundColor: selectedBgColor.value,
              boxShadow: '0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(0,0,0,0.05)',
            }"
          >
            <image
              :src="selectedImage"
              mode="aspectFill"
              class="h-full w-full"
            />
            <!-- 重新选择按钮 -->
            <view
              class="absolute right-2 top-2 h-8 w-8 flex items-center justify-center rounded-full bg-black bg-opacity-50"
              @click="handleSelectPhoto"
            >
              <view class="text-sm text-white">
                ✏️
              </view>
            </view>
          </view>

          <view
            v-else
            class="relative h-80 w-60 flex flex-col items-center justify-center border-2 border-gray-300 rounded-2xl border-dashed from-gray-50 to-gray-100 bg-gradient-to-br transition-all duration-300 hover:border-blue-400 hover:from-blue-50 hover:to-indigo-50 hover:bg-gradient-to-br"
            @click="handleSelectPhoto"
          >
            <!-- 背景装饰 -->
            <view class="absolute inset-0 rounded-2xl from-blue-500 to-indigo-500 bg-gradient-to-br opacity-5" />

            <view class="relative z-10 flex flex-col items-center">
              <view class="mb-4 text-6xl">
                📸
              </view>
              <view class="mb-2 text-lg text-gray-700 font-semibold">
                选择您的照片
              </view>
              <view class="px-4 text-center text-sm text-gray-500">
                支持拍照或从相册选择
              </view>
              <view class="mt-4 rounded-full from-blue-500 to-indigo-500 bg-gradient-to-r px-6 py-2">
                <view class="text-sm text-white font-medium">
                  点击开始
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 尺寸选择 -->
      <view class="mb-8 border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 flex items-center">
          <view class="mr-3 h-6 w-1 rounded-full from-purple-500 to-pink-500 bg-gradient-to-b" />
          <view class="text-xl text-gray-800 font-bold">
            选择尺寸
          </view>
        </view>
        <view class="grid grid-cols-2 gap-4">
          <view
            v-for="size in photoSizes"
            :key="size.name"
            class="relative cursor-pointer overflow-hidden border-2 rounded-xl p-4 text-center transition-all duration-300"
            :class="selectedSize.name === size.name
              ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg transform scale-105'
              : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-md hover:transform hover:scale-102'"
            @click="handleSelectSize(size)"
          >
            <!-- 选中状态指示器 -->
            <view
              v-if="selectedSize.name === size.name"
              class="absolute right-2 top-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-500"
            >
              <view class="text-xs text-white">
                ✓
              </view>
            </view>

            <view class="mb-1 text-lg text-gray-800 font-bold">
              {{ size.name }}
            </view>
            <view class="text-sm text-gray-500">
              {{ size.desc }}
            </view>

            <!-- 装饰性图标 -->
            <view class="mt-2 text-2xl opacity-60">
              📏
            </view>
          </view>
        </view>
      </view>

      <!-- 背景颜色选择 -->
      <view class="mb-8 border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 flex items-center">
          <view class="mr-3 h-6 w-1 rounded-full from-green-500 to-teal-500 bg-gradient-to-b" />
          <view class="text-xl text-gray-800 font-bold">
            背景颜色
          </view>
        </view>
        <view class="flex justify-around">
          <view
            v-for="color in backgroundColors"
            :key="color.name"
            class="flex flex-col cursor-pointer items-center transition-all duration-300"
            @click="handleSelectBgColor(color)"
          >
            <view class="relative mb-3">
              <view
                class="h-16 w-16 border-4 rounded-2xl shadow-lg transition-all duration-300"
                :class="[
                  color.class,
                  selectedBgColor.name === color.name
                    ? 'border-blue-500 transform scale-110 shadow-xl'
                    : 'border-gray-200 hover:border-gray-300 hover:transform hover:scale-105',
                ]"
              />
              <!-- 选中状态指示器 -->
              <view
                v-if="selectedBgColor.name === color.name"
                class="absolute h-6 w-6 flex items-center justify-center rounded-full bg-blue-500 shadow-lg -right-1 -top-1"
              >
                <view class="text-xs text-white">
                  ✓
                </view>
              </view>
            </view>
            <view
              class="text-sm font-medium transition-colors duration-300"
              :class="selectedBgColor.name === color.name ? 'text-blue-600' : 'text-gray-600'"
            >
              {{ color.name }}
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="mb-8 space-y-4">
        <!-- 选择照片按钮 -->
        <view class="relative overflow-hidden rounded-2xl">
          <view class="absolute inset-0 from-blue-500 to-indigo-500 bg-gradient-to-r opacity-90" />
          <wd-button
            custom-class="!bg-transparent !border-none !text-white !font-bold !text-lg !py-4"
            size="large"
            block
            :loading="uploadLoading"
            @click="handleSelectPhoto"
          >
            <view class="flex items-center justify-center">
              <view class="mr-2 text-xl">
                {{ selectedImage ? '🔄' : '📷' }}
              </view>
              {{ selectedImage ? '重新选择照片' : '选择照片' }}
            </view>
          </wd-button>
        </view>

        <!-- 制作证件照按钮 -->
        <view class="relative overflow-hidden rounded-2xl">
          <view
            class="absolute inset-0 transition-all duration-300"
            :class="!selectedImage ? 'bg-gray-300' : 'bg-gradient-to-r from-green-500 to-emerald-500'"
          />
          <wd-button
            custom-class="!bg-transparent !border-none !font-bold !text-lg !py-4"
            :custom-class="!selectedImage ? '!text-gray-500' : '!text-white'"
            size="large"
            block
            :loading="isProcessing"
            :disabled="!selectedImage"
            @click="handleMakePhoto"
          >
            <view class="flex items-center justify-center">
              <view class="mr-2 text-xl">
                {{ isProcessing ? '⚡' : '✨' }}
              </view>
              {{ isProcessing ? '制作中...' : '制作证件照' }}
            </view>
          </wd-button>
        </view>
      </view>

      <!-- 功能特色 -->
      <view class="border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 flex items-center">
          <view class="mr-3 h-6 w-1 rounded-full from-orange-500 to-red-500 bg-gradient-to-b" />
          <view class="text-xl text-gray-800 font-bold">
            功能特色
          </view>
        </view>

        <view class="grid grid-cols-1 gap-4">
          <view class="flex items-start rounded-xl from-blue-50 to-indigo-50 bg-gradient-to-r p-3">
            <view class="mr-3 text-2xl">
              🎯
            </view>
            <view>
              <view class="mb-1 text-sm text-gray-800 font-semibold">
                多种尺寸规格
              </view>
              <view class="text-xs text-gray-600">
                支持一寸、二寸等常用证件照尺寸
              </view>
            </view>
          </view>

          <view class="flex items-start rounded-xl from-purple-50 to-pink-50 bg-gradient-to-r p-3">
            <view class="mr-3 text-2xl">
              🎨
            </view>
            <view>
              <view class="mb-1 text-sm text-gray-800 font-semibold">
                智能背景替换
              </view>
              <view class="text-xs text-gray-600">
                AI自动识别人像，一键更换背景颜色
              </view>
            </view>
          </view>

          <view class="flex items-start rounded-xl from-green-50 to-teal-50 bg-gradient-to-r p-3">
            <view class="mr-3 text-2xl">
              ✨
            </view>
            <view>
              <view class="mb-1 text-sm text-gray-800 font-semibold">
                专业品质输出
              </view>
              <view class="text-xs text-gray-600">
                高清画质，满足各类证件照要求
              </view>
            </view>
          </view>

          <view class="flex items-start rounded-xl from-orange-50 to-yellow-50 bg-gradient-to-r p-3">
            <view class="mr-3 text-2xl">
              ⚡
            </view>
            <view>
              <view class="mb-1 text-sm text-gray-800 font-semibold">
                快速便捷
              </view>
              <view class="text-xs text-gray-600">
                一键生成，无需复杂操作
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "证件照制作"
  }
}
</route>

<script lang="ts" setup>
// import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets: any
let systemInfo: any

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 热门证件照类型
const hotPhotoTypes = [
  { id: 1, name: '国家公务员（小...', size: '25×35mm | 295×413px', hot: true },
  { id: 2, name: '一寸', size: '25×35mm | 295×413px', hot: true },
  { id: 3, name: '大一寸', size: '33×48mm | 390×567px', hot: false },
  { id: 4, name: '小一寸', size: '22×32mm | 260×378px', hot: false },
  { id: 5, name: '硕士研究生考...', size: '25×35mm | 295×413px', hot: true },
  { id: 6, name: '二寸', size: '35×49mm | 413×579px', hot: false },
  { id: 7, name: '简历照片', size: '25×35mm | 295×413px', hot: false },
  { id: 8, name: '国家司法考试', size: '25×35mm | 295×413px', hot: false },
  { id: 9, name: '教师资格证', size: '25×35mm | 295×413px', hot: false },
  { id: 10, name: '社保证（300dp...', size: '25×35mm | 295×413px', hot: false },
]

// 快捷功能
const quickActions = [
  {
    id: 1,
    name: '热门考试',
    icon: '📝',
    desc: '公务员、教师资格证等',
    path: '/pages/exam/index',
  },
  {
    id: 2,
    name: '常用规格',
    icon: '📐',
    desc: '一寸、二寸等标准规格',
    path: '/pages/standard/index',
  },
  {
    id: 3,
    name: '各类签证',
    icon: '🛂',
    desc: '美签、日签等签证照片',
    path: '/pages/visa/index',
  },
  {
    id: 4,
    name: '客服中心',
    icon: '🎧',
    desc: '在线客服，解答疑问',
    path: '/pages/service/index',
  },
]

// 主要功能按钮
const mainActions = [
  {
    id: 1,
    name: '拍摄证件照',
    desc: '下载电子版，冲印纸质照',
    icon: '📷',
    color: 'from-purple-500 to-indigo-500',
    path: '/pages/camera/index',
  },
  {
    id: 2,
    name: '来集证件照',
    desc: '来集拍照站 | 专人证件照',
    icon: '👥',
    color: 'from-red-500 to-pink-500',
    path: '/pages/studio/index',
  },
]

// 热门证件照规格
const popularSizes = [
  {
    id: 1,
    name: '一寸',
    size: '25×35mm | 295×413px',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 2,
    name: '二寸',
    size: '35×49mm | 413×579px',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 3,
    name: '原图换底',
    size: '不改尺寸，只换底色',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 4,
    name: '全国计算机等级考试',
    size: '33×48mm | 390×567px',
    bgColors: ['电子照', '冲印照'],
  },
]

// 跳转到制作页面
function goToMake(type: any) {
  uni.navigateTo({
    url: `/pages/make/index?type=${type.id}&name=${type.name}`,
  })
}

// 跳转到功能页面
function goToPage(path: string) {
  uni.navigateTo({
    url: path,
  })
}
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 头部横幅 -->
    <view class="relative overflow-hidden from-pink-100 to-purple-100 bg-gradient-to-r px-4 py-6">
      <!-- 装饰元素 -->
      <view class="absolute right-4 top-4 text-4xl opacity-30">
        🦋
      </view>
      <view class="absolute bottom-2 left-8 text-2xl opacity-20">
        ✨
      </view>
      <view class="absolute right-20 top-8 text-2xl text-yellow-400">
        ⭐
      </view>

      <view class="flex items-center justify-between">
        <view>
          <view class="mb-1 text-3xl text-blue-600 font-bold">
            证件照
          </view>
          <view class="flex items-center">
            <view class="text-lg text-orange-500 font-semibold">
              免费制作
            </view>
            <view class="ml-2 text-sm text-gray-600">
              🎉
            </view>
          </view>
        </view>
        <view class="flex space-x-2">
          <!-- 证件照示例 -->
          <view class="relative">
            <view class="h-20 w-16 overflow-hidden border-2 border-white rounded-lg bg-white shadow-lg">
              <view class="h-full w-full flex items-center justify-center from-blue-100 to-blue-200 bg-gradient-to-b">
                <view class="text-2xl">
                  👤
                </view>
              </view>
            </view>
            <view class="absolute h-4 w-4 flex items-center justify-center rounded-full bg-red-500 -right-1 -top-1">
              <view class="text-xs text-white">
                HOT
              </view>
            </view>
          </view>
          <view class="relative">
            <view class="h-20 w-16 overflow-hidden border-2 border-white rounded-lg bg-white shadow-lg">
              <view class="h-full w-full flex items-center justify-center from-red-100 to-red-200 bg-gradient-to-b">
                <view class="text-2xl">
                  👩
                </view>
              </view>
            </view>
            <view class="absolute h-4 w-4 flex items-center justify-center rounded-full bg-green-500 -right-1 -top-1">
              <view class="text-xs text-white">
                NEW
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要功能区域 -->
    <view class="px-4 py-4">
      <!-- 快捷功能 -->
      <view class="grid grid-cols-4 mb-6 gap-4">
        <view
          v-for="action in quickActions"
          :key="action.id"
          class="flex flex-col items-center rounded-xl bg-white p-3 shadow-sm"
          @click="goToPage(action.path)"
        >
          <view class="mb-2 text-2xl">
            {{ action.icon }}
          </view>
          <view class="text-center text-sm text-gray-700 font-medium">
            {{ action.name }}
          </view>
        </view>
      </view>

      <!-- 主要操作按钮 -->
      <view class="grid grid-cols-2 mb-6 gap-4">
        <view
          v-for="action in mainActions"
          :key="action.id"
          class="relative transform overflow-hidden rounded-2xl p-6 text-white shadow-lg transition-all duration-300 hover:scale-105"
          :class="`bg-gradient-to-br ${action.color}`"
          @click="goToPage(action.path)"
        >
          <!-- 装饰性背景图案 -->
          <view class="absolute right-0 top-0 h-16 w-16 opacity-20">
            <view class="h-full w-full translate-x-8 rotate-45 transform border-4 border-white rounded-full -translate-y-8" />
          </view>

          <view class="relative z-10">
            <view class="mb-3 text-4xl">
              {{ action.icon }}
            </view>
            <view class="mb-2 text-lg font-bold">
              {{ action.name }}
            </view>
            <view class="text-sm leading-relaxed opacity-90">
              {{ action.desc }}
            </view>
          </view>
        </view>
      </view>

      <!-- 热门证件照 -->
      <view class="mb-6 rounded-xl bg-white p-4 shadow-sm">
        <view class="mb-4 flex items-center justify-between">
          <view class="flex items-center">
            <view class="text-lg text-gray-800 font-bold">
              热门证件
            </view>
            <view class="ml-2 rounded-full bg-red-500 px-2 py-1 text-xs text-white">
              HOT
            </view>
          </view>
          <view class="flex items-center text-sm text-gray-400">
            <view>更多尺寸</view>
            <view class="ml-1">
              >
            </view>
          </view>
        </view>

        <!-- 热门证件照网格 -->
        <view class="grid grid-cols-2 gap-3">
          <view
            v-for="(type, index) in hotPhotoTypes.slice(0, 10)"
            :key="type.id"
            class="flex items-center rounded-lg bg-gray-50 p-3"
            @click="goToMake(type)"
          >
            <view class="mr-3 text-sm text-red-500 font-bold">
              {{ String(index + 1).padStart(2, '0') }}
            </view>
            <view class="flex-1">
              <view class="mb-1 text-sm text-gray-800 font-medium">
                {{ type.name }}
              </view>
              <view class="text-xs text-gray-500">
                {{ type.size }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 热门证件照规格 -->
      <view class="rounded-xl bg-white p-4 shadow-sm">
        <view class="mb-4 text-lg text-gray-800 font-bold">
          热门证件照
        </view>

        <view class="grid grid-cols-2 gap-4">
          <view
            v-for="size in popularSizes"
            :key="size.id"
            class="border border-gray-200 rounded-xl p-4"
            @click="goToMake(size)"
          >
            <view class="mb-2 flex items-center justify-between">
              <view class="text-lg text-gray-800 font-bold">
                {{ size.name }}
              </view>
              <view v-if="size.id <= 2" class="rounded-full bg-red-500 px-2 py-1 text-xs text-white">
                HOT
              </view>
            </view>
            <view class="mb-3 text-sm text-gray-500">
              {{ size.size }}
            </view>
            <view class="flex gap-2">
              <view
                v-for="bgType in size.bgColors"
                :key="bgType"
                class="rounded-full px-3 py-1 text-xs"
                :class="bgType === '电子照' ? 'bg-blue-100 text-blue-600' : 'bg-red-100 text-red-600'"
              >
                {{ bgType }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

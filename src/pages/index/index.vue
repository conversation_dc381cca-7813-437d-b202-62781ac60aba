<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "证件照制作"
  }
}
</route>

<script lang="ts" setup>
// import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets: any
let systemInfo: any

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 热门证件照类型
const hotPhotoTypes = [
  { id: 1, name: '国家公务员（小...', size: '25×35mm | 295×413px', hot: true },
  { id: 2, name: '一寸', size: '25×35mm | 295×413px', hot: true },
  { id: 3, name: '大一寸', size: '33×48mm | 390×567px', hot: false },
  { id: 4, name: '小一寸', size: '22×32mm | 260×378px', hot: false },
  { id: 5, name: '硕士研究生考...', size: '25×35mm | 295×413px', hot: true },
  { id: 6, name: '二寸', size: '35×49mm | 413×579px', hot: false },
  { id: 7, name: '简历照片', size: '25×35mm | 295×413px', hot: false },
  { id: 8, name: '国家司法考试', size: '25×35mm | 295×413px', hot: false },
  { id: 9, name: '教师资格证', size: '25×35mm | 295×413px', hot: false },
  { id: 10, name: '社保证（300dp...', size: '25×35mm | 295×413px', hot: false },
]

// 删除未使用的变量

// 热门证件照规格
const popularSizes = [
  {
    id: 1,
    name: '一寸',
    size: '25×35mm | 295×413px',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 2,
    name: '二寸',
    size: '35×49mm | 413×579px',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 3,
    name: '原图换底',
    size: '不改尺寸，只换底色',
    bgColors: ['电子照', '冲印照'],
  },
  {
    id: 4,
    name: '全国计算机等级考试',
    size: '33×48mm | 390×567px',
    bgColors: ['电子照', '冲印照'],
  },
]

// 跳转到制作页面
function goToMake(type: any) {
  uni.navigateTo({
    url: `/pages/make/index?type=${type.id}&name=${type.name}`,
  })
}

// 跳转到功能页面
function goToPage(path: string) {
  uni.navigateTo({
    url: path,
  })
}
</script>

<template>
  <view class="min-h-screen from-blue-50 via-white to-purple-50 bg-gradient-to-br" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 精美头部区域 -->
    <view class="relative overflow-hidden from-indigo-600 via-purple-600 to-pink-600 bg-gradient-to-r px-6 py-10">
      <!-- 背景装饰 -->
      <view class="absolute inset-0 bg-black opacity-10" />
      <view class="absolute h-40 w-40 rounded-full bg-white opacity-10 -right-10 -top-10" />
      <view class="absolute h-20 w-20 rounded-full bg-yellow-300 opacity-20 -bottom-5 -left-5" />

      <view class="relative z-10 text-center">
        <view class="mb-3 text-4xl text-white font-bold">
          ✨ AI智能证件照
        </view>
        <view class="mb-6 text-lg text-purple-100">
          专业品质 · 一键生成 · 多种规格
        </view>
        <view class="flex justify-center space-x-4">
          <view class="rounded-full bg-white bg-opacity-20 px-4 py-2 backdrop-blur-sm">
            <view class="text-sm text-white">
              🎯 精准识别
            </view>
          </view>
          <view class="rounded-full bg-white bg-opacity-20 px-4 py-2 backdrop-blur-sm">
            <view class="text-sm text-white">
              ⚡ 秒速生成
            </view>
          </view>
        </view>
      </view>

      <!-- 波浪装饰 -->
      <view
        class="absolute bottom-0 left-0 h-6 w-full from-blue-50 via-white to-purple-50 bg-gradient-to-br"
        style="clip-path: polygon(0 100%, 100% 0, 100% 100%)"
      />
    </view>

    <!-- 主要内容区域 -->
    <view class="px-4 py-6 -mt-3">
      <!-- 快速制作入口 -->
      <view class="mb-8 border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 text-center">
          <view class="mb-2 text-2xl text-gray-800 font-bold">
            开始制作证件照
          </view>
          <view class="text-gray-500">
            选择规格，一键生成专业证件照
          </view>
        </view>

        <view class="grid grid-cols-2 gap-4">
          <view
            class="relative transform overflow-hidden rounded-xl from-blue-500 to-indigo-600 bg-gradient-to-br p-6 text-white shadow-lg transition-all duration-300 hover:scale-105"
            @click="goToPage('/pages/camera/index')"
          >
            <view class="absolute right-0 top-0 h-20 w-20 translate-x-6 transform rounded-full bg-white opacity-10 -translate-y-6" />
            <view class="relative z-10">
              <view class="mb-3 text-3xl">
                📷
              </view>
              <view class="mb-1 text-lg font-bold">
                拍照制作
              </view>
              <view class="text-sm opacity-90">
                现拍现做
              </view>
            </view>
          </view>

          <view
            class="relative transform overflow-hidden rounded-xl from-purple-500 to-pink-600 bg-gradient-to-br p-6 text-white shadow-lg transition-all duration-300 hover:scale-105"
            @click="goToPage('/pages/upload/index')"
          >
            <view class="absolute right-0 top-0 h-20 w-20 translate-x-6 transform rounded-full bg-white opacity-10 -translate-y-6" />
            <view class="relative z-10">
              <view class="mb-3 text-3xl">
                🖼️
              </view>
              <view class="mb-1 text-lg font-bold">
                相册选择
              </view>
              <view class="text-sm opacity-90">
                从相册选图
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 热门证件照类型 -->
      <view class="mb-8 border border-gray-100 rounded-2xl bg-white p-6 shadow-lg">
        <view class="mb-6 flex items-center justify-between">
          <view class="flex items-center">
            <view class="mr-3 h-6 w-1 rounded-full from-red-500 to-pink-500 bg-gradient-to-b" />
            <view class="text-xl text-gray-800 font-bold">
              热门证件照
            </view>
            <view class="ml-3 rounded-full from-red-500 to-pink-500 bg-gradient-to-r px-3 py-1 text-xs text-white shadow-sm">
              🔥 HOT
            </view>
          </view>
          <view class="flex cursor-pointer items-center text-sm text-gray-400" @click="goToPage('/pages/more/index')">
            <view>查看全部</view>
            <view class="ml-1 text-lg">
              ›
            </view>
          </view>
        </view>

        <!-- 热门证件照网格 -->
        <view class="grid grid-cols-1 gap-3">
          <view
            v-for="(type, index) in hotPhotoTypes.slice(0, 8)"
            :key="type.id"
            class="flex items-center border border-gray-100 rounded-xl from-gray-50 to-blue-50 bg-gradient-to-r p-4 transition-all duration-300 hover:scale-102 hover:shadow-md"
            @click="goToMake(type)"
          >
            <view class="mr-4 h-8 w-8 flex items-center justify-center rounded-lg from-blue-500 to-indigo-600 bg-gradient-to-br text-sm text-white font-bold">
              {{ String(index + 1).padStart(2, '0') }}
            </view>
            <view class="flex-1">
              <view class="mb-1 text-base text-gray-800 font-semibold">
                {{ type.name }}
              </view>
              <view class="text-sm text-gray-500">
                {{ type.size }}
              </view>
            </view>
            <view class="text-gray-400">
              <view class="text-xl">
                ›
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部提示 -->
      <view class="py-8 text-center">
        <view class="mb-2 text-sm text-gray-400">
          更多规格和功能正在开发中...
        </view>
        <view class="text-xs text-gray-300">
          🎯 专业品质 · ⚡ 快速生成 · 🎨 智能美化
        </view>
      </view>
    </view>
  </view>
</template>
